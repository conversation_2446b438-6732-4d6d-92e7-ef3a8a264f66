{"version": 3, "names": ["React", "TabAnimationContext", "useTabAnimation", "animation", "useContext", "undefined", "Error"], "sourceRoot": "../../../src", "sources": ["utils/useTabAnimation.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,mBAAmB,QAAQ,0BAAuB;AAE3D,OAAO,SAASC,eAAeA,CAAA,EAAG;EAChC,MAAMC,SAAS,GAAGH,KAAK,CAACI,UAAU,CAACH,mBAAmB,CAAC;EAEvD,IAAIE,SAAS,KAAKE,SAAS,EAAE;IAC3B,MAAM,IAAIC,KAAK,CACb,gGACF,CAAC;EACH;EAEA,OAAOH,SAAS;AAClB", "ignoreList": []}