{"version": 3, "names": ["Text", "useLinkBuilder", "useLocale", "useTheme", "Color", "StyleSheet", "TabBar", "TabBarIndicator", "jsx", "_jsx", "renderLabelDefault", "color", "labelText", "style", "allowFontScaling", "styles", "label", "children", "MaterialTopTabBar", "state", "navigation", "descriptors", "rest", "colors", "direction", "buildHref", "focusedOptions", "routes", "index", "key", "options", "activeColor", "tabBarActiveTintColor", "text", "inactiveColor", "tabBarInactiveTintColor", "alpha", "rgb", "string", "tabBarOptions", "Object", "fromEntries", "map", "route", "title", "tabBarLabel", "tabBarButtonTestID", "tabBarAccessibilityLabel", "tabBarBadge", "tabBarShowIcon", "tabBarShowLabel", "tabBarIcon", "tabBarAllowFontScaling", "tabBarLabelStyle", "href", "name", "params", "testID", "accessibilityLabel", "badge", "icon", "undefined", "focused", "labelAllowFontScaling", "labelStyle", "navigationState", "scrollEnabled", "tabBarScrollEnabled", "bounces", "tabBarBounces", "pressColor", "tabBarPressColor", "pressOpacity", "tabBarPressOpacity", "tabStyle", "tabBarItemStyle", "indicatorStyle", "backgroundColor", "primary", "tabBarIndicatorStyle", "gap", "tabBarGap", "android_ripple", "tabBarAndroidRipple", "indicatorContainerStyle", "tabBarIndicatorContainerStyle", "contentContainerStyle", "tabBarContentContainerStyle", "card", "tabBarStyle", "onTabPress", "preventDefault", "event", "emit", "type", "target", "canPreventDefault", "defaultPrevented", "onTabLongPress", "renderIndicator", "tabBarIndicator", "create", "textAlign", "fontSize", "margin"], "sourceRoot": "../../../src", "sources": ["views/MaterialTopTabBar.tsx"], "mappings": ";;AAAA,SAASA,IAAI,QAAQ,4BAA4B;AACjD,SAGEC,cAAc,EACdC,SAAS,EACTC,QAAQ,QACH,0BAA0B;AACjC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,QAAQ,cAAc;AACzC,SAEEC,MAAM,EACNC,eAAe,QAEV,uBAAuB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAQ/B,MAAMC,kBAAkB,GAAGA,CAAC;EAC1BC,KAAK;EACLC,SAAS;EACTC,KAAK;EACLC;AACkB,CAAC,KAAK;EACxB,oBACEL,IAAA,CAACT,IAAI;IACHa,KAAK,EAAE,CAAC;MAAEF;IAAM,CAAC,EAAEI,MAAM,CAACC,KAAK,EAAEH,KAAK,CAAE;IACxCC,gBAAgB,EAAEA,gBAAiB;IAAAG,QAAA,EAElCL;EAAS,CACN,CAAC;AAEX,CAAC;AAED,OAAO,SAASM,iBAAiBA,CAAC;EAChCC,KAAK;EACLC,UAAU;EACVC,WAAW;EACX,GAAGC;AACmB,CAAC,EAAE;EACzB,MAAM;IAAEC;EAAO,CAAC,GAAGpB,QAAQ,CAAC,CAAC;EAC7B,MAAM;IAAEqB;EAAU,CAAC,GAAGtB,SAAS,CAAC,CAAC;EACjC,MAAM;IAAEuB;EAAU,CAAC,GAAGxB,cAAc,CAAC,CAAC;EAEtC,MAAMyB,cAAc,GAAGL,WAAW,CAACF,KAAK,CAACQ,MAAM,CAACR,KAAK,CAACS,KAAK,CAAC,CAACC,GAAG,CAAC,CAACC,OAAO;EAEzE,MAAMC,WAAW,GAAGL,cAAc,CAACM,qBAAqB,IAAIT,MAAM,CAACU,IAAI;EACvE,MAAMC,aAAa,GACjBR,cAAc,CAACS,uBAAuB,IACtC/B,KAAK,CAAC2B,WAAW,CAAC,CAACK,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EAE9C,MAAMC,aAAa,GAAGC,MAAM,CAACC,WAAW,CACtCtB,KAAK,CAACQ,MAAM,CAACe,GAAG,CAAEC,KAAK,IAAK;IAC1B,MAAM;MAAEb;IAAQ,CAAC,GAAGT,WAAW,CAACsB,KAAK,CAACd,GAAG,CAAC;IAE1C,MAAM;MACJe,KAAK;MACLC,WAAW;MACXC,kBAAkB;MAClBC,wBAAwB;MACxBC,WAAW;MACXC,cAAc;MACdC,eAAe;MACfC,UAAU;MACVC,sBAAsB;MACtBC;IACF,CAAC,GAAGvB,OAAO;IAEX,OAAO,CACLa,KAAK,CAACd,GAAG,EACT;MACEyB,IAAI,EAAE7B,SAAS,CAACkB,KAAK,CAACY,IAAI,EAAEZ,KAAK,CAACa,MAAM,CAAC;MACzCC,MAAM,EAAEX,kBAAkB;MAC1BY,kBAAkB,EAAEX,wBAAwB;MAC5CY,KAAK,EAAEX,WAAW;MAClBY,IAAI,EAAEX,cAAc,KAAK,KAAK,GAAGY,SAAS,GAAGV,UAAU;MACvDnC,KAAK,EACHkC,eAAe,KAAK,KAAK,GACrBW,SAAS,GACT,OAAOhB,WAAW,KAAK,UAAU,GAC/B,CAAC;QAAEjC,SAAS;QAAED;MAA0B,CAAC,KACvCkC,WAAW,CAAC;QACViB,OAAO,EAAE3C,KAAK,CAACQ,MAAM,CAACR,KAAK,CAACS,KAAK,CAAC,CAACC,GAAG,KAAKc,KAAK,CAACd,GAAG;QACpDlB,KAAK;QACLM,QAAQ,EAAEL,SAAS,IAAI+B,KAAK,CAACY;MAC/B,CAAC,CAAC,GACJ7C,kBAAkB;MAC1BqD,qBAAqB,EAAEX,sBAAsB;MAC7CY,UAAU,EAAEX,gBAAgB;MAC5BzC,SAAS,EACPkB,OAAO,CAACoB,eAAe,KAAK,KAAK,GAC7BW,SAAS,GACT,OAAOhB,WAAW,KAAK,QAAQ,GAC7BA,WAAW,GACXD,KAAK,KAAKiB,SAAS,GACjBjB,KAAK,GACLD,KAAK,CAACY;IAClB,CAAC,CACF;EACH,CAAC,CACH,CAAC;EAED,oBACE9C,IAAA,CAACH,MAAM;IAAA,GACDgB,IAAI;IACR2C,eAAe,EAAE9C,KAAM;IACvBW,OAAO,EAAES,aAAc;IACvBf,SAAS,EAAEA,SAAU;IACrB0C,aAAa,EAAExC,cAAc,CAACyC,mBAAoB;IAClDC,OAAO,EAAE1C,cAAc,CAAC2C,aAAc;IACtCtC,WAAW,EAAEA,WAAY;IACzBG,aAAa,EAAEA,aAAc;IAC7BoC,UAAU,EAAE5C,cAAc,CAAC6C,gBAAiB;IAC5CC,YAAY,EAAE9C,cAAc,CAAC+C,kBAAmB;IAChDC,QAAQ,EAAEhD,cAAc,CAACiD,eAAgB;IACzCC,cAAc,EAAE,CACd;MAAEC,eAAe,EAAEtD,MAAM,CAACuD;IAAQ,CAAC,EACnCpD,cAAc,CAACqD,oBAAoB,CACnC;IACFC,GAAG,EAAEtD,cAAc,CAACuD,SAAU;IAC9BC,cAAc,EAAExD,cAAc,CAACyD,mBAAoB;IACnDC,uBAAuB,EAAE1D,cAAc,CAAC2D,6BAA8B;IACtEC,qBAAqB,EAAE5D,cAAc,CAAC6D,2BAA4B;IAClE1E,KAAK,EAAE,CAAC;MAAEgE,eAAe,EAAEtD,MAAM,CAACiE;IAAK,CAAC,EAAE9D,cAAc,CAAC+D,WAAW,CAAE;IACtEC,UAAU,EAAEA,CAAC;MAAE/C,KAAK;MAAEgD;IAAe,CAAC,KAAK;MACzC,MAAMC,KAAK,GAAGxE,UAAU,CAACyE,IAAI,CAAC;QAC5BC,IAAI,EAAE,UAAU;QAChBC,MAAM,EAAEpD,KAAK,CAACd,GAAG;QACjBmE,iBAAiB,EAAE;MACrB,CAAC,CAAC;MAEF,IAAIJ,KAAK,CAACK,gBAAgB,EAAE;QAC1BN,cAAc,CAAC,CAAC;MAClB;IACF,CAAE;IACFO,cAAc,EAAEA,CAAC;MAAEvD;IAAM,CAAC,KACxBvB,UAAU,CAACyE,IAAI,CAAC;MACdC,IAAI,EAAE,cAAc;MACpBC,MAAM,EAAEpD,KAAK,CAACd;IAChB,CAAC,CACF;IACDsE,eAAe,EAAEA,CAAC;MAAElC,eAAe,EAAE9C,KAAK;MAAE,GAAGG;IAAK,CAAC,KAAK;MACxD,OAAOI,cAAc,CAAC0E,eAAe,GACnC1E,cAAc,CAAC0E,eAAe,CAAC;QAC7BjF,KAAK,EAAEA,KAA0C;QACjD,GAAGG;MACL,CAAC,CAAC,gBAEFb,IAAA,CAACF,eAAe;QAAC0D,eAAe,EAAE9C,KAAM;QAAA,GAAKG;MAAI,CAAG,CACrD;IACH;EAAE,CACH,CAAC;AAEN;AAEA,MAAMP,MAAM,GAAGV,UAAU,CAACgG,MAAM,CAAC;EAC/BrF,KAAK,EAAE;IACLsF,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE,CAAC;IACT3B,eAAe,EAAE;EACnB;AACF,CAAC,CAAC", "ignoreList": []}