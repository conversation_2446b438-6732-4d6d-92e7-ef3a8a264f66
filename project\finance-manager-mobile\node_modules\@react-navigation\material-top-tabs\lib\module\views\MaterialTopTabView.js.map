{"version": 3, "names": ["CommonActions", "useLocale", "useTheme", "TabView", "TabAnimationContext", "MaterialTopTabBar", "jsx", "_jsx", "renderTabBarDefault", "props", "MaterialTopTabView", "tabBar", "state", "navigation", "descriptors", "rest", "colors", "direction", "renderTabBar", "navigationState", "options", "focusedOptions", "routes", "index", "key", "onIndexChange", "route", "dispatch", "navigate", "target", "renderScene", "position", "Provider", "value", "children", "render", "renderLazyPlaceholder", "lazyPlaceholder", "lazy", "preloadedRouteKeys", "includes", "lazyPreloadDistance", "swipeEnabled", "animationEnabled", "onSwipeStart", "emit", "type", "onSwipeEnd", "Object", "fromEntries", "map", "sceneStyle", "backgroundColor", "background"], "sourceRoot": "../../../src", "sources": ["views/MaterialTopTabView.tsx"], "mappings": ";;AAAA,SACEA,aAAa,EAIbC,SAAS,EACTC,QAAQ,QACH,0BAA0B;AACjC,SAASC,OAAO,QAAQ,uBAAuB;AAQ/C,SAASC,mBAAmB,QAAQ,iCAA8B;AAClE,SAASC,iBAAiB,QAAQ,wBAAqB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAQxD,MAAMC,mBAAmB,GAAIC,KAA6B,iBACxDF,IAAA,CAACF,iBAAiB;EAAA,GAAKI;AAAK,CAAG,CAChC;AAED,OAAO,SAASC,kBAAkBA,CAAC;EACjCC,MAAM,GAAGH,mBAAmB;EAC5BI,KAAK;EACLC,UAAU;EACVC,WAAW;EACX,GAAGC;AACE,CAAC,EAAE;EACR,MAAM;IAAEC;EAAO,CAAC,GAAGd,QAAQ,CAAC,CAAC;EAC7B,MAAM;IAAEe;EAAU,CAAC,GAAGhB,SAAS,CAAC,CAAC;EAEjC,MAAMiB,YAEW,GAAGA,CAAC;IACnB;IACAC,eAAe;IACfC,OAAO;IACP;IACA,GAAGL;EACL,CAAC,KAAK;IACJ,OAAOJ,MAAM,CAAC;MACZ,GAAGI,IAAI;MACPH,KAAK,EAAEA,KAAK;MACZC,UAAU,EAAEA,UAAU;MACtBC,WAAW,EAAEA;IACf,CAAC,CAAC;EACJ,CAAC;EAED,MAAMO,cAAc,GAAGP,WAAW,CAACF,KAAK,CAACU,MAAM,CAACV,KAAK,CAACW,KAAK,CAAC,CAACC,GAAG,CAAC,CAACJ,OAAO;EAEzE,oBACEb,IAAA,CAACJ,OAAO;IAAA,GACFY,IAAI;IACRU,aAAa,EAAGF,KAAK,IAAK;MACxB,MAAMG,KAAK,GAAGd,KAAK,CAACU,MAAM,CAACC,KAAK,CAAC;MAEjCV,UAAU,CAACc,QAAQ,CAAC;QAClB,GAAG3B,aAAa,CAAC4B,QAAQ,CAACF,KAAK,CAAC;QAChCG,MAAM,EAAEjB,KAAK,CAACY;MAChB,CAAC,CAAC;IACJ,CAAE;IACFM,WAAW,EAAEA,CAAC;MAAEJ,KAAK;MAAEK;IAAS,CAAC,kBAC/BxB,IAAA,CAACH,mBAAmB,CAAC4B,QAAQ;MAACC,KAAK,EAAE;QAAEF;MAAS,CAAE;MAAAG,QAAA,EAC/CpB,WAAW,CAACY,KAAK,CAACF,GAAG,CAAC,CAACW,MAAM,CAAC;IAAC,CACJ,CAC9B;IACFhB,eAAe,EAAEP,KAAM;IACvBM,YAAY,EAAEA,YAAa;IAC3BkB,qBAAqB,EAAEA,CAAC;MAAEV;IAAM,CAAC,KAC/BZ,WAAW,CAACY,KAAK,CAACF,GAAG,CAAC,CAACJ,OAAO,CAACiB,eAAe,GAAG,CAAC,IAAI,IACvD;IACDC,IAAI,EAAEA,CAAC;MAAEZ;IAAM,CAAC,KACdZ,WAAW,CAACY,KAAK,CAACF,GAAG,CAAC,CAACJ,OAAO,CAACkB,IAAI,KAAK,IAAI,IAC5C,CAAC1B,KAAK,CAAC2B,kBAAkB,CAACC,QAAQ,CAACd,KAAK,CAACF,GAAG,CAC7C;IACDiB,mBAAmB,EAAEpB,cAAc,CAACoB,mBAAoB;IACxDC,YAAY,EAAErB,cAAc,CAACqB,YAAa;IAC1CC,gBAAgB,EAAEtB,cAAc,CAACsB,gBAAiB;IAClDC,YAAY,EAAEA,CAAA,KAAM/B,UAAU,CAACgC,IAAI,CAAC;MAAEC,IAAI,EAAE;IAAa,CAAC,CAAE;IAC5DC,UAAU,EAAEA,CAAA,KAAMlC,UAAU,CAACgC,IAAI,CAAC;MAAEC,IAAI,EAAE;IAAW,CAAC,CAAE;IACxD7B,SAAS,EAAEA,SAAU;IACrBG,OAAO,EAAE4B,MAAM,CAACC,WAAW,CACzBrC,KAAK,CAACU,MAAM,CAAC4B,GAAG,CAAExB,KAAK,IAAK;MAC1B,MAAMN,OAAO,GAAGN,WAAW,CAACY,KAAK,CAACF,GAAG,CAAC,EAAEJ,OAAO;MAE/C,OAAO,CACLM,KAAK,CAACF,GAAG,EACT;QACE2B,UAAU,EAAE,CACV;UAAEC,eAAe,EAAEpC,MAAM,CAACqC;QAAW,CAAC,EACtCjC,OAAO,EAAE+B,UAAU;MAEvB,CAAC,CACF;IACH,CAAC,CACH;EAAE,CACH,CAAC;AAEN", "ignoreList": []}