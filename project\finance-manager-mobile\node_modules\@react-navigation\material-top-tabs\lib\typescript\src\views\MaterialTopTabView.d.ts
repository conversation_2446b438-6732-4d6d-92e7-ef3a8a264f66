import { type ParamListBase, type TabNavigationState } from '@react-navigation/native';
import type { MaterialTopTabDescriptorMap, MaterialTopTabNavigationConfig, MaterialTopTabNavigationHelpers } from '../types';
type Props = MaterialTopTabNavigationConfig & {
    state: TabNavigationState<ParamListBase>;
    navigation: MaterialTopTabNavigationHelpers;
    descriptors: MaterialTopTabDescriptorMap;
};
export declare function MaterialTopTabView({ tabBar, state, navigation, descriptors, ...rest }: Props): import("react/jsx-runtime").JSX.Element;
export {};
//# sourceMappingURL=MaterialTopTabView.d.ts.map