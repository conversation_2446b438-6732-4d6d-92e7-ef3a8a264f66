{"version": 3, "names": ["_codegenNativeCommands", "_interopRequireDefault", "require", "_codegenNativeComponent", "e", "__esModule", "default", "Commands", "exports", "codegenNativeCommands", "supportedCommands", "_default", "codegenNativeComponent"], "sources": ["PagerViewNativeComponent.ts"], "sourcesContent": ["import type * as React from 'react';\nimport type { HostComponent, ViewProps } from 'react-native';\nimport codegenNativeCommands from 'react-native/Libraries/Utilities/codegenNativeCommands';\nimport codegenNativeComponent from 'react-native/Libraries/Utilities/codegenNativeComponent';\n\nimport type {\n  DirectEventHandler,\n  Double,\n  Int32,\n  WithDefault,\n} from 'react-native/Libraries/Types/CodegenTypes';\n\nexport type OnPageScrollEventData = Readonly<{\n  position: Double;\n  offset: Double;\n}>;\n\nexport type OnPageSelectedEventData = Readonly<{\n  position: Double;\n}>;\n\nexport type OnPageScrollStateChangedEventData = Readonly<{\n  pageScrollState: 'idle' | 'dragging' | 'settling';\n}>;\n\nexport interface NativeProps extends ViewProps {\n  scrollEnabled?: WithDefault<boolean, true>;\n  layoutDirection?: WithDefault<'ltr' | 'rtl', 'ltr'>;\n  initialPage?: Int32;\n  orientation?: WithDefault<'horizontal' | 'vertical', 'horizontal'>;\n  offscreenPageLimit?: Int32;\n  pageMargin?: Int32;\n  overScrollMode?: WithDefault<'auto' | 'always' | 'never', 'auto'>;\n  overdrag?: WithDefault<boolean, false>;\n  keyboardDismissMode?: WithDefault<'none' | 'on-drag', 'none'>;\n  onPageScroll?: DirectEventHandler<OnPageScrollEventData>;\n  onPageSelected?: DirectEventHandler<OnPageSelectedEventData>;\n  onPageScrollStateChanged?: DirectEventHandler<OnPageScrollStateChangedEventData>;\n}\n\ntype PagerViewViewType = HostComponent<NativeProps>;\n\nexport interface NativeCommands {\n  setPage: (\n    viewRef: React.ElementRef<PagerViewViewType>,\n    selectedPage: Int32\n  ) => void;\n  setPageWithoutAnimation: (\n    viewRef: React.ElementRef<PagerViewViewType>,\n    selectedPage: Int32\n  ) => void;\n  setScrollEnabledImperatively: (\n    viewRef: React.ElementRef<PagerViewViewType>,\n    scrollEnabled: boolean\n  ) => void;\n}\n\nexport const Commands: NativeCommands = codegenNativeCommands<NativeCommands>({\n  supportedCommands: [\n    'setPage',\n    'setPageWithoutAnimation',\n    'setScrollEnabledImperatively',\n  ],\n});\n\nexport default codegenNativeComponent<NativeProps>(\n  'RNCViewPager'\n) as HostComponent<NativeProps>;\n"], "mappings": ";;;;;;AAEA,IAAAA,sBAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,uBAAA,GAAAF,sBAAA,CAAAC,OAAA;AAA6F,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAsDtF,MAAMG,QAAwB,GAAAC,OAAA,CAAAD,QAAA,GAAG,IAAAE,8BAAqB,EAAiB;EAC5EC,iBAAiB,EAAE,CACjB,SAAS,EACT,yBAAyB,EACzB,8BAA8B;AAElC,CAAC,CAAC;AAAC,IAAAC,QAAA,GAAAH,OAAA,CAAAF,OAAA,GAEY,IAAAM,+BAAsB,EACnC,cACF,CAAC", "ignoreList": []}