import codegenNativeCommands from 'react-native/Libraries/Utilities/codegenNativeCommands';
import codegenNativeComponent from 'react-native/Libraries/Utilities/codegenNativeComponent';
export const Commands = codegenNativeCommands({
  supportedCommands: ['setPage', 'setPageWithoutAnimation', 'setScrollEnabledImperatively']
});
export default codegenNativeComponent('RNCViewPager');
//# sourceMappingURL=PagerViewNativeComponent.js.map