{"version": 3, "names": ["React", "Animated", "Keyboard", "PanResponder", "StyleSheet", "View", "useLatestCallback", "useAnimatedValue", "jsx", "_jsx", "DEAD_ZONE", "DefaultTransitionSpec", "timing", "spring", "stiffness", "damping", "mass", "overshootClamping", "PanResponderAdapter", "layout", "keyboardDismissMode", "swipeEnabled", "navigationState", "onIndexChange", "onTabSelect", "onSwipeStart", "onSwipeEnd", "children", "style", "animationEnabled", "layoutDirection", "routes", "index", "panX", "listenersRef", "useRef", "navigationStateRef", "layoutRef", "onIndexChangeRef", "onTabSelectRef", "currentIndexRef", "pendingIndexRef", "undefined", "swipeVelocityThreshold", "swipeDistanceThreshold", "width", "jumpToIndex", "animate", "offset", "current", "transitionConfig", "parallel", "toValue", "useNativeDriver", "start", "finished", "setValue", "useEffect", "dismiss", "isMovingHorizontally", "_", "gestureState", "Math", "abs", "dx", "dy", "vx", "vy", "canMoveScreen", "event", "diffX", "length", "startGesture", "stopAnimation", "setOffset", "_value", "respondToGesture", "position", "_offset", "next", "ceil", "floor", "for<PERSON>ach", "listener", "finishGesture", "flattenOffset", "currentIndex", "nextIndex", "round", "min", "max", "isFinite", "addEnterListener", "push", "indexOf", "splice", "jumpTo", "key", "findIndex", "route", "panResponder", "create", "onMoveShouldSetPanResponder", "onMoveShouldSetPanResponderCapture", "onPanResponderGrant", "onPanResponderMove", "onPanResponderTerminate", "onPanResponderRelease", "onPanResponderTerminationRequest", "maxTranslate", "translateX", "multiply", "interpolate", "inputRange", "outputRange", "extrapolate", "useMemo", "divide", "Value", "render", "styles", "sheet", "transform", "panHandlers", "Children", "map", "child", "i", "focused", "absoluteFill", "flex", "flexDirection", "alignItems"], "sourceRoot": "../../src", "sources": ["PanResponderAdapter.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SACEC,QAAQ,EAERC,QAAQ,EACRC,YAAY,EAEZC,UAAU,EACVC,IAAI,QACC,cAAc;AACrB,OAAOC,iBAAiB,MAAM,qBAAqB;AAUnD,SAASC,gBAAgB,QAAQ,uBAAoB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAsBtD,MAAMC,SAAS,GAAG,EAAE;AAEpB,MAAMC,qBAAqB,GAAG;EAC5BC,MAAM,EAAEX,QAAQ,CAACY,MAAM;EACvBC,SAAS,EAAE,IAAI;EACfC,OAAO,EAAE,GAAG;EACZC,IAAI,EAAE,CAAC;EACPC,iBAAiB,EAAE;AACrB,CAAC;AAED,OAAO,SAASC,mBAAmBA,CAAkB;EACnDC,MAAM;EACNC,mBAAmB,GAAG,MAAM;EAC5BC,YAAY,GAAG,IAAI;EACnBC,eAAe;EACfC,aAAa;EACbC,WAAW;EACXC,YAAY;EACZC,UAAU;EACVC,QAAQ;EACRC,KAAK;EACLC,gBAAgB,GAAG,KAAK;EACxBC,eAAe,GAAG;AACV,CAAC,EAAE;EACX,MAAM;IAAEC,MAAM;IAAEC;EAAM,CAAC,GAAGV,eAAe;EAEzC,MAAMW,IAAI,GAAG1B,gBAAgB,CAAC,CAAC,CAAC;EAEhC,MAAM2B,YAAY,GAAGlC,KAAK,CAACmC,MAAM,CAAa,EAAE,CAAC;EAEjD,MAAMC,kBAAkB,GAAGpC,KAAK,CAACmC,MAAM,CAACb,eAAe,CAAC;EACxD,MAAMe,SAAS,GAAGrC,KAAK,CAACmC,MAAM,CAAChB,MAAM,CAAC;EACtC,MAAMmB,gBAAgB,GAAGtC,KAAK,CAACmC,MAAM,CAACZ,aAAa,CAAC;EACpD,MAAMgB,cAAc,GAAGvC,KAAK,CAACmC,MAAM,CAACX,WAAW,CAAC;EAChD,MAAMgB,eAAe,GAAGxC,KAAK,CAACmC,MAAM,CAACH,KAAK,CAAC;EAC3C,MAAMS,eAAe,GAAGzC,KAAK,CAACmC,MAAM,CAASO,SAAS,CAAC;EAEvD,MAAMC,sBAAsB,GAAG,IAAI;EACnC,MAAMC,sBAAsB,GAAGzB,MAAM,CAAC0B,KAAK,GAAG,IAAI;EAElD,MAAMC,WAAW,GAAGxC,iBAAiB,CACnC,CAAC0B,KAAa,EAAEe,OAAO,GAAGlB,gBAAgB,KAAK;IAC7C,MAAMmB,MAAM,GAAG,CAAChB,KAAK,GAAGK,SAAS,CAACY,OAAO,CAACJ,KAAK;IAE/C,MAAM;MAAEjC,MAAM;MAAE,GAAGsC;IAAiB,CAAC,GAAGvC,qBAAqB;IAE7D,IAAIoC,OAAO,EAAE;MACX9C,QAAQ,CAACkD,QAAQ,CAAC,CAChBvC,MAAM,CAACqB,IAAI,EAAE;QACX,GAAGiB,gBAAgB;QACnBE,OAAO,EAAEJ,MAAM;QACfK,eAAe,EAAE;MACnB,CAAC,CAAC,CACH,CAAC,CAACC,KAAK,CAAC,CAAC;QAAEC;MAAS,CAAC,KAAK;QACzB,IAAIA,QAAQ,EAAE;UACZjB,gBAAgB,CAACW,OAAO,CAACjB,KAAK,CAAC;UAC/BO,cAAc,CAACU,OAAO,GAAG;YAAEjB;UAAM,CAAC,CAAC;UACnCS,eAAe,CAACQ,OAAO,GAAGP,SAAS;QACrC;MACF,CAAC,CAAC;MACFD,eAAe,CAACQ,OAAO,GAAGjB,KAAK;IACjC,CAAC,MAAM;MACLC,IAAI,CAACuB,QAAQ,CAACR,MAAM,CAAC;MACrBV,gBAAgB,CAACW,OAAO,CAACjB,KAAK,CAAC;MAC/BO,cAAc,CAACU,OAAO,GAAG;QAAEjB;MAAM,CAAC,CAAC;MACnCS,eAAe,CAACQ,OAAO,GAAGP,SAAS;IACrC;EACF,CACF,CAAC;EAED1C,KAAK,CAACyD,SAAS,CAAC,MAAM;IACpBrB,kBAAkB,CAACa,OAAO,GAAG3B,eAAe;IAC5Ce,SAAS,CAACY,OAAO,GAAG9B,MAAM;IAC1BmB,gBAAgB,CAACW,OAAO,GAAG1B,aAAa;IACxCgB,cAAc,CAACU,OAAO,GAAGzB,WAAW;EACtC,CAAC,CAAC;EAEFxB,KAAK,CAACyD,SAAS,CAAC,MAAM;IACpB,MAAMT,MAAM,GAAG,CAACZ,kBAAkB,CAACa,OAAO,CAACjB,KAAK,GAAGb,MAAM,CAAC0B,KAAK;IAE/DZ,IAAI,CAACuB,QAAQ,CAACR,MAAM,CAAC;EACvB,CAAC,EAAE,CAAC7B,MAAM,CAAC0B,KAAK,EAAEZ,IAAI,CAAC,CAAC;EAExBjC,KAAK,CAACyD,SAAS,CAAC,MAAM;IACpB,IAAIrC,mBAAmB,KAAK,MAAM,EAAE;MAClClB,QAAQ,CAACwD,OAAO,CAAC,CAAC;IACpB;IAEA,IAAIvC,MAAM,CAAC0B,KAAK,IAAIL,eAAe,CAACS,OAAO,KAAKjB,KAAK,EAAE;MACrDQ,eAAe,CAACS,OAAO,GAAGjB,KAAK;MAC/Bc,WAAW,CAACd,KAAK,CAAC;IACpB;EACF,CAAC,EAAE,CAACc,WAAW,EAAE1B,mBAAmB,EAAED,MAAM,CAAC0B,KAAK,EAAEb,KAAK,CAAC,CAAC;EAE3D,MAAM2B,oBAAoB,GAAGA,CAC3BC,CAAwB,EACxBC,YAAsC,KACnC;IACH,OACEC,IAAI,CAACC,GAAG,CAACF,YAAY,CAACG,EAAE,CAAC,GAAGF,IAAI,CAACC,GAAG,CAACF,YAAY,CAACI,EAAE,GAAG,CAAC,CAAC,IACzDH,IAAI,CAACC,GAAG,CAACF,YAAY,CAACK,EAAE,CAAC,GAAGJ,IAAI,CAACC,GAAG,CAACF,YAAY,CAACM,EAAE,GAAG,CAAC,CAAC;EAE7D,CAAC;EAED,MAAMC,aAAa,GAAGA,CACpBC,KAA4B,EAC5BR,YAAsC,KACnC;IACH,IAAIxC,YAAY,KAAK,KAAK,EAAE;MAC1B,OAAO,KAAK;IACd;IAEA,MAAMiD,KAAK,GACTxC,eAAe,KAAK,KAAK,GAAG,CAAC+B,YAAY,CAACG,EAAE,GAAGH,YAAY,CAACG,EAAE;IAEhE,OACEL,oBAAoB,CAACU,KAAK,EAAER,YAAY,CAAC,KACvCS,KAAK,IAAI5D,SAAS,IAAI8B,eAAe,CAACS,OAAO,GAAG,CAAC,IAChDqB,KAAK,IAAI,CAAC5D,SAAS,IAAI8B,eAAe,CAACS,OAAO,GAAGlB,MAAM,CAACwC,MAAM,GAAG,CAAE,CAAC;EAE3E,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB/C,YAAY,GAAG,CAAC;IAEhB,IAAIL,mBAAmB,KAAK,SAAS,EAAE;MACrClB,QAAQ,CAACwD,OAAO,CAAC,CAAC;IACpB;IAEAzB,IAAI,CAACwC,aAAa,CAAC,CAAC;IACpB;IACAxC,IAAI,CAACyC,SAAS,CAACzC,IAAI,CAAC0C,MAAM,CAAC;EAC7B,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CACvBhB,CAAwB,EACxBC,YAAsC,KACnC;IACH,MAAMS,KAAK,GACTxC,eAAe,KAAK,KAAK,GAAG,CAAC+B,YAAY,CAACG,EAAE,GAAGH,YAAY,CAACG,EAAE;IAEhE;IACE;IACCM,KAAK,GAAG,CAAC,IAAItC,KAAK,IAAI,CAAC;IACxB;IACCsC,KAAK,GAAG,CAAC,IAAItC,KAAK,IAAID,MAAM,CAACwC,MAAM,GAAG,CAAE,EACzC;MACA;IACF;IAEA,IAAIpD,MAAM,CAAC0B,KAAK,EAAE;MAChB;MACA,MAAMgC,QAAQ,GAAG,CAAC5C,IAAI,CAAC6C,OAAO,GAAGR,KAAK,IAAI,CAACnD,MAAM,CAAC0B,KAAK;MACvD,MAAMkC,IAAI,GACRF,QAAQ,GAAG7C,KAAK,GAAG8B,IAAI,CAACkB,IAAI,CAACH,QAAQ,CAAC,GAAGf,IAAI,CAACmB,KAAK,CAACJ,QAAQ,CAAC;MAE/D,IAAIE,IAAI,KAAK/C,KAAK,EAAE;QAClBE,YAAY,CAACe,OAAO,CAACiC,OAAO,CAAEC,QAAQ,IAAKA,QAAQ,CAACJ,IAAI,CAAC,CAAC;MAC5D;IACF;IAEA9C,IAAI,CAACuB,QAAQ,CAACc,KAAK,CAAC;EACtB,CAAC;EAED,MAAMc,aAAa,GAAGA,CACpBxB,CAAwB,EACxBC,YAAsC,KACnC;IACH5B,IAAI,CAACoD,aAAa,CAAC,CAAC;IAEpB3D,UAAU,GAAG,CAAC;IAEd,MAAM4D,YAAY,GAChB,OAAO7C,eAAe,CAACQ,OAAO,KAAK,QAAQ,GACvCR,eAAe,CAACQ,OAAO,GACvBT,eAAe,CAACS,OAAO;IAE7B,IAAIsC,SAAS,GAAGD,YAAY;IAE5B,IACExB,IAAI,CAACC,GAAG,CAACF,YAAY,CAACG,EAAE,CAAC,GAAGF,IAAI,CAACC,GAAG,CAACF,YAAY,CAACI,EAAE,CAAC,IACrDH,IAAI,CAACC,GAAG,CAACF,YAAY,CAACK,EAAE,CAAC,GAAGJ,IAAI,CAACC,GAAG,CAACF,YAAY,CAACM,EAAE,CAAC,KACpDL,IAAI,CAACC,GAAG,CAACF,YAAY,CAACG,EAAE,CAAC,GAAGpB,sBAAsB,IACjDkB,IAAI,CAACC,GAAG,CAACF,YAAY,CAACK,EAAE,CAAC,GAAGvB,sBAAsB,CAAC,EACrD;MACA4C,SAAS,GAAGzB,IAAI,CAAC0B,KAAK,CACpB1B,IAAI,CAAC2B,GAAG,CACN3B,IAAI,CAAC4B,GAAG,CACN,CAAC,EACD5D,eAAe,KAAK,KAAK,GACrBwD,YAAY,GAAGzB,YAAY,CAACG,EAAE,GAAGF,IAAI,CAACC,GAAG,CAACF,YAAY,CAACG,EAAE,CAAC,GAC1DsB,YAAY,GAAGzB,YAAY,CAACG,EAAE,GAAGF,IAAI,CAACC,GAAG,CAACF,YAAY,CAACG,EAAE,CAC/D,CAAC,EACDjC,MAAM,CAACwC,MAAM,GAAG,CAClB,CACF,CAAC;MAED/B,eAAe,CAACS,OAAO,GAAGsC,SAAS;IACrC;IAEA,IAAI,CAACI,QAAQ,CAACJ,SAAS,CAAC,EAAE;MACxBA,SAAS,GAAGD,YAAY;IAC1B;IAEAxC,WAAW,CAACyC,SAAS,EAAE,IAAI,CAAC;EAC9B,CAAC;EAED,MAAMK,gBAAgB,GAAGtF,iBAAiB,CAAE6E,QAAkB,IAAK;IACjEjD,YAAY,CAACe,OAAO,CAAC4C,IAAI,CAACV,QAAQ,CAAC;IAEnC,OAAO,MAAM;MACX,MAAMnD,KAAK,GAAGE,YAAY,CAACe,OAAO,CAAC6C,OAAO,CAACX,QAAQ,CAAC;MAEpD,IAAInD,KAAK,GAAG,CAAC,CAAC,EAAE;QACdE,YAAY,CAACe,OAAO,CAAC8C,MAAM,CAAC/D,KAAK,EAAE,CAAC,CAAC;MACvC;IACF,CAAC;EACH,CAAC,CAAC;EAEF,MAAMgE,MAAM,GAAG1F,iBAAiB,CAAE2F,GAAW,IAAK;IAChD,MAAMjE,KAAK,GAAGI,kBAAkB,CAACa,OAAO,CAAClB,MAAM,CAACmE,SAAS,CACtDC,KAAsB,IAAKA,KAAK,CAACF,GAAG,KAAKA,GAC5C,CAAC;IAEDnD,WAAW,CAACd,KAAK,CAAC;IAClBT,aAAa,CAACS,KAAK,CAAC;EACtB,CAAC,CAAC;EAEF,MAAMoE,YAAY,GAAGjG,YAAY,CAACkG,MAAM,CAAC;IACvCC,2BAA2B,EAAElC,aAAa;IAC1CmC,kCAAkC,EAAEnC,aAAa;IACjDoC,mBAAmB,EAAEhC,YAAY;IACjCiC,kBAAkB,EAAE7B,gBAAgB;IACpC8B,uBAAuB,EAAEtB,aAAa;IACtCuB,qBAAqB,EAAEvB,aAAa;IACpCwB,gCAAgC,EAAEA,CAAA,KAAM;EAC1C,CAAC,CAAC;EAEF,MAAMC,YAAY,GAAG1F,MAAM,CAAC0B,KAAK,IAAId,MAAM,CAACwC,MAAM,GAAG,CAAC,CAAC;EACvD,MAAMuC,UAAU,GAAG7G,QAAQ,CAAC8G,QAAQ,CAClC9E,IAAI,CAAC+E,WAAW,CAAC;IACfC,UAAU,EAAE,CAAC,CAACJ,YAAY,EAAE,CAAC,CAAC;IAC9BK,WAAW,EAAE,CAAC,CAACL,YAAY,EAAE,CAAC,CAAC;IAC/BM,WAAW,EAAE;EACf,CAAC,CAAC,EACFrF,eAAe,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,CACnC,CAAC;EAED,MAAM+C,QAAQ,GAAG7E,KAAK,CAACoH,OAAO,CAC5B,MAAOjG,MAAM,CAAC0B,KAAK,GAAG5C,QAAQ,CAACoH,MAAM,CAACpF,IAAI,EAAE,CAACd,MAAM,CAAC0B,KAAK,CAAC,GAAG,IAAK,EAClE,CAAC1B,MAAM,CAAC0B,KAAK,EAAEZ,IAAI,CACrB,CAAC;EAED,OAAON,QAAQ,CAAC;IACdkD,QAAQ,EAAEA,QAAQ,IAAI,IAAI5E,QAAQ,CAACqH,KAAK,CAACtF,KAAK,CAAC;IAC/C4D,gBAAgB;IAChBI,MAAM;IACNuB,MAAM,EAAG5F,QAAQ,iBACflB,IAAA,CAACR,QAAQ,CAACI,IAAI;MACZuB,KAAK,EAAE,CACL4F,MAAM,CAACC,KAAK,EACZtG,MAAM,CAAC0B,KAAK,GACR;QACEA,KAAK,EAAEd,MAAM,CAACwC,MAAM,GAAGpD,MAAM,CAAC0B,KAAK;QACnC6E,SAAS,EAAE,CAAC;UAAEZ;QAAW,CAAC;MAC5B,CAAC,GACD,IAAI,EACRlF,KAAK,CACL;MAAA,GACEwE,YAAY,CAACuB,WAAW;MAAAhG,QAAA,EAE3B3B,KAAK,CAAC4H,QAAQ,CAACC,GAAG,CAAClG,QAAQ,EAAE,CAACmG,KAAK,EAAEC,CAAC,KAAK;QAC1C,MAAM5B,KAAK,GAAGpE,MAAM,CAACgG,CAAC,CAAC;QACvB,MAAMC,OAAO,GAAGD,CAAC,KAAK/F,KAAK;QAE3B,oBACEvB,IAAA,CAACJ,IAAI;UAEHuB,KAAK,EACHT,MAAM,CAAC0B,KAAK,GACR;YAAEA,KAAK,EAAE1B,MAAM,CAAC0B;UAAM,CAAC,GACvBmF,OAAO,GACL5H,UAAU,CAAC6H,YAAY,GACvB,IACP;UAAAtG,QAAA,EAEAqG,OAAO,IAAI7G,MAAM,CAAC0B,KAAK,GAAGiF,KAAK,GAAG;QAAI,GATlC3B,KAAK,CAACF,GAUP,CAAC;MAEX,CAAC;IAAC,CACW;EAEnB,CAAC,CAAC;AACJ;AAEA,MAAMuB,MAAM,GAAGpH,UAAU,CAACiG,MAAM,CAAC;EAC/BoB,KAAK,EAAE;IACLS,IAAI,EAAE,CAAC;IACPC,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE;EACd;AACF,CAAC,CAAC", "ignoreList": []}