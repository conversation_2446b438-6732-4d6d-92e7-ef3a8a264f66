{"version": 3, "names": ["React", "Animated", "StyleSheet", "jsx", "_jsx", "TabBarItemLabel", "memo", "color", "label", "style", "icon", "Text", "styles", "marginTop", "children", "displayName", "create", "margin", "fontSize", "fontWeight", "backgroundColor"], "sourceRoot": "../../src", "sources": ["TabBarItemLabel.tsx"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,SAASC,QAAQ,EAAEC,UAAU,QAAQ,cAAc;AAAC,SAAAC,GAAA,IAAAC,IAAA;AASpD,OAAO,MAAMC,eAAe,gBAAGL,KAAK,CAACM,IAAI,CACvC,CAAC;EAAEC,KAAK;EAAEC,KAAK;EAAEC,KAAK;EAAEC;AAA2B,CAAC,KAAK;EACvD,IAAI,CAACF,KAAK,EAAE;IACV,OAAO,IAAI;EACb;EAEA,oBACEJ,IAAA,CAACH,QAAQ,CAACU,IAAI;IACZF,KAAK,EAAE,CACLG,MAAM,CAACJ,KAAK,EACZE,IAAI,GAAG;MAAEG,SAAS,EAAE;IAAE,CAAC,GAAG,IAAI,EAC9BJ,KAAK,EACL;MAAEF,KAAK,EAAEA;IAAM,CAAC,CAChB;IAAAO,QAAA,EAEDN;EAAK,CACO,CAAC;AAEpB,CACF,CAAC;AAEDH,eAAe,CAACU,WAAW,GAAG,iBAAiB;AAE/C,MAAMH,MAAM,GAAGV,UAAU,CAACc,MAAM,CAAC;EAC/BR,KAAK,EAAE;IACLS,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,KAAK;IACjBC,eAAe,EAAE;EACnB;AACF,CAAC,CAAC", "ignoreList": []}