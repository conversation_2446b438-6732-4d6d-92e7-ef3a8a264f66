{"version": 3, "names": ["React", "Animated", "useAnimatedValue", "initialValue", "lazyRef", "useRef", "undefined", "current", "Value"], "sourceRoot": "../../src", "sources": ["useAnimatedValue.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,cAAc;AAEvC,OAAO,SAASC,gBAAgBA,CAACC,YAAoB,EAAE;EACrD,MAAMC,OAAO,GAAGJ,KAAK,CAACK,MAAM,CAAiBC,SAAS,CAAC;EAEvD,IAAIF,OAAO,CAACG,OAAO,KAAKD,SAAS,EAAE;IACjCF,OAAO,CAACG,OAAO,GAAG,IAAIN,QAAQ,CAACO,KAAK,CAACL,YAAY,CAAC;EACpD;EAEA,OAAOC,OAAO,CAACG,OAAO;AACxB", "ignoreList": []}