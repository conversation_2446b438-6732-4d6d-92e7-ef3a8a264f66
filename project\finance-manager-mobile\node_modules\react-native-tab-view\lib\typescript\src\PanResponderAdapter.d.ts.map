{"version": 3, "file": "PanResponderAdapter.d.ts", "sourceRoot": "", "sources": ["../../../src/PanResponderAdapter.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAC/B,OAAO,EACL,QAAQ,EAOT,MAAM,cAAc,CAAC;AAGtB,OAAO,KAAK,EACV,iBAAiB,EACjB,MAAM,EAEN,eAAe,EACf,UAAU,EACV,KAAK,EACN,MAAM,SAAS,CAAC;AAGjB,KAAK,KAAK,CAAC,CAAC,SAAS,KAAK,IAAI,UAAU,GAAG;IACzC,MAAM,EAAE,MAAM,CAAC;IACf,aAAa,EAAE,CAAC,KAAK,EAAE,MAAM,KAAK,IAAI,CAAC;IACvC,WAAW,CAAC,EAAE,CAAC,KAAK,EAAE;QAAE,KAAK,EAAE,MAAM,CAAA;KAAE,KAAK,IAAI,CAAC;IACjD,eAAe,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC;IACpC,QAAQ,EAAE,CACR,KAAK,EAAE,iBAAiB,GAAG;QAGzB,QAAQ,EAAE,QAAQ,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;QAGjD,MAAM,EAAE,CAAC,QAAQ,EAAE,KAAK,CAAC,SAAS,KAAK,KAAK,CAAC,SAAS,CAAC;QAGvD,MAAM,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK,IAAI,CAAC;KAC/B,KACE,KAAK,CAAC,YAAY,CAAC;CACzB,CAAC;AAYF,wBAAgB,mBAAmB,CAAC,CAAC,SAAS,KAAK,EAAE,EACnD,MAAM,EACN,mBAA4B,EAC5B,YAAmB,EACnB,eAAe,EACf,aAAa,EACb,WAAW,EACX,YAAY,EACZ,UAAU,EACV,QAAQ,EACR,KAAK,EACL,gBAAwB,EACxB,eAAuB,GACxB,EAAE,KAAK,CAAC,CAAC,CAAC,0EA8QV"}