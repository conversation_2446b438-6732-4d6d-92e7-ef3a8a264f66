import React from 'react';
import type { StyleProp, ViewStyle } from 'react-native';
interface TabBarItemLabelProps {
    color: string;
    label?: string;
    style: StyleProp<ViewStyle>;
    icon: React.ReactNode;
}
export declare const TabBarItemLabel: React.MemoExoticComponent<({ color, label, style, icon }: TabBarItemLabelProps) => import("react/jsx-runtime").JSX.Element | null>;
export {};
//# sourceMappingURL=TabBarItemLabel.d.ts.map