{"version": 3, "file": "TabView.d.ts", "sourceRoot": "", "sources": ["../../../src/TabView.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAC/B,OAAO,EAIL,KAAK,SAAS,EAGd,KAAK,SAAS,EACf,MAAM,cAAc,CAAC;AAKtB,OAAO,KAAK,EACV,MAAM,EACN,eAAe,EACf,eAAe,EACf,UAAU,EACV,KAAK,EACL,kBAAkB,EAClB,aAAa,EACd,MAAM,SAAS,CAAC;AAEjB,MAAM,MAAM,KAAK,CAAC,CAAC,SAAS,KAAK,IAAI,IAAI,CAAC,UAAU,EAAE,iBAAiB,CAAC,GAAG;IACzE,aAAa,EAAE,CAAC,KAAK,EAAE,MAAM,KAAK,IAAI,CAAC;IACvC,WAAW,CAAC,EAAE,CAAC,KAAK,EAAE;QAAE,KAAK,EAAE,MAAM,CAAA;KAAE,KAAK,IAAI,CAAC;IACjD,eAAe,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC;IACpC,qBAAqB,CAAC,EAAE,CAAC,KAAK,EAAE;QAAE,KAAK,EAAE,CAAC,CAAA;KAAE,KAAK,KAAK,CAAC,SAAS,CAAC;IACjE,YAAY,CAAC,EAAE,CACb,KAAK,EAAE,kBAAkB,GAAG;QAC1B,eAAe,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC;QACpC,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC;KACvD,KACE,KAAK,CAAC,SAAS,CAAC;IACrB,cAAc,CAAC,EAAE,KAAK,GAAG,QAAQ,CAAC;IAClC,aAAa,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;IAChC,IAAI,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE;QAAE,KAAK,EAAE,CAAC,CAAA;KAAE,KAAK,OAAO,CAAC,GAAG,OAAO,CAAC;IACpD,mBAAmB,CAAC,EAAE,MAAM,CAAC;IAC7B,SAAS,CAAC,EAAE,eAAe,CAAC;IAC5B,UAAU,CAAC,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;IAClC,KAAK,CAAC,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;IAC7B,WAAW,EAAE,CAAC,KAAK,EAAE,kBAAkB,GAAG;QAAE,KAAK,EAAE,CAAC,CAAA;KAAE,KAAK,KAAK,CAAC,SAAS,CAAC;IAC3E,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3C,aAAa,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC;CAClC,CAAC;AAIF,wBAAgB,OAAO,CAAC,CAAC,SAAS,KAAK,EAAE,EACvC,aAAa,EACb,WAAW,EACX,eAAe,EACf,WAAW,EACX,aAAa,EACb,mBAA4B,EAC5B,IAAY,EACZ,mBAAuB,EACvB,YAAY,EACZ,UAAU,EACV,qBAAoD,EAEpD,YAA+C,EAC/C,UAAU,EACV,KAAK,EACL,SAA4D,EAC5D,YAAmB,EACnB,cAAsB,EACtB,gBAAuB,EACvB,cAAc,EACd,OAAO,EAAE,YAAY,EACrB,aAAa,GACd,EAAE,KAAK,CAAC,CAAC,CAAC,2CAsHV"}