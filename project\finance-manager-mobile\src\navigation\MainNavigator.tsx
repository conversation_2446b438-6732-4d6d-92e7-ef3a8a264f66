import React from 'react';
import { createDrawerNavigator } from '@react-navigation/drawer';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { View, Text, TouchableOpacity, StyleSheet, Dimensions } from 'react-native';
import { colors } from '../constants/colors';

// Import tab screens
import DashboardNavigator from './DashboardNavigator';
import AccountsNavigator from './AccountsNavigator';
import TransactionsNavigator from './TransactionsNavigator';
import GoalsNavigator from './GoalsNavigator';
import MoreNavigator from './MoreNavigator';

// Import drawer screens
import StatementImportScreen from '../screens/statements/StatementImportScreen';
import NotificationCenterScreen from '../screens/notifications/NotificationCenterScreen';
import RecommendationsHistoryScreen from '../screens/recommendations/RecommendationsHistoryScreen';
import PremiumUpgradeScreen from '../screens/premium/PremiumUpgradeScreen';
import SettingsScreen from '../screens/settings/SettingsScreen';
import ProfileScreen from '../screens/profile/ProfileScreen';
import AccountSharesScreen from '../screens/premium/AccountSharesScreen';
import HelpSupportScreen from '../screens/help/HelpSupportScreen';

const Drawer = createDrawerNavigator();
const Tab = createMaterialTopTabNavigator();
const Stack = createStackNavigator();

const { width: screenWidth } = Dimensions.get('window');

// Tab Bar Icon Component
const TabBarIcon: React.FC<{ name: string; focused: boolean }> = ({ name, focused }) => {
  const iconMap: { [key: string]: string } = {
    Dashboard: '🏠',
    Accounts: '💼',
    Transactions: '📋',
    Goals: '🎯',
    More: '☰',
  };

  return (
    <View style={styles.tabIconContainer}>
      <Text style={[styles.tabIcon, { opacity: focused ? 1 : 0.6 }]}>
        {iconMap[name] || '📱'}
      </Text>
      <Text style={[styles.tabLabel, { color: focused ? colors.primary : colors.textSecondary }]}>
        {name}
      </Text>
    </View>
  );
};

// Custom Bottom Tab Bar Component
const CustomBottomTabBar: React.FC<any> = ({ state, descriptors, navigation }) => {
  return (
    <View style={styles.customTabBar}>
      {state.routes.map((route: any, index: number) => {
        const { options } = descriptors[route.key];
        const label = options.tabBarLabel !== undefined 
          ? options.tabBarLabel 
          : options.title !== undefined 
          ? options.title 
          : route.name;

        const isFocused = state.index === index;

        const onPress = () => {
          const event = navigation.emit({
            type: 'tabPress',
            target: route.key,
            canPreventDefault: true,
          });

          if (!isFocused && !event.defaultPrevented) {
            navigation.jumpTo(route.name);
          }
        };

        const onLongPress = () => {
          navigation.emit({
            type: 'tabLongPress',
            target: route.key,
          });
        };

        return (
          <TouchableOpacity
            key={route.key}
            accessibilityRole="button"
            accessibilityState={isFocused ? { selected: true } : {}}
            accessibilityLabel={options.tabBarAccessibilityLabel}
            testID={options.tabBarTestID}
            onPress={onPress}
            onLongPress={onLongPress}
            style={styles.tabButton}
          >
            <TabBarIcon name={label} focused={isFocused} />
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

// Swipeable Tab Navigator
const TabNavigator: React.FC = () => {
  return (
    <Tab.Navigator
      tabBarPosition="bottom"
      tabBar={(props) => <CustomBottomTabBar {...props} />}
      screenOptions={{
        swipeEnabled: true,
        lazy: true,
        lazyPreloadDistance: 1,
        animationEnabled: true,
      }}
      initialLayout={{ width: screenWidth }}
    >
      <Tab.Screen 
        name="Dashboard" 
        component={DashboardNavigator}
        options={{ tabBarLabel: 'Dashboard' }}
      />
      <Tab.Screen 
        name="Accounts" 
        component={AccountsNavigator}
        options={{ tabBarLabel: 'Accounts' }}
      />
      <Tab.Screen 
        name="Transactions" 
        component={TransactionsNavigator}
        options={{ tabBarLabel: 'Transactions' }}
      />
      <Tab.Screen 
        name="Goals" 
        component={GoalsNavigator}
        options={{ tabBarLabel: 'Goals' }}
      />
      <Tab.Screen 
        name="More" 
        component={MoreNavigator}
        options={{ tabBarLabel: 'More' }}
      />
    </Tab.Navigator>
  );
};

// Main Navigator with Drawer
const MainNavigator: React.FC = () => {
  return (
    <Drawer.Navigator
      screenOptions={{
        headerShown: false,
        drawerStyle: {
          backgroundColor: colors.background,
          width: 280,
        },
        drawerActiveTintColor: colors.primary,
        drawerInactiveTintColor: colors.textSecondary,
      }}
    >
      <Drawer.Screen
        name="Home"
        component={TabNavigator}
        options={{
          drawerLabel: 'Home',
          drawerIcon: () => <Text style={{ fontSize: 20 }}>🏠</Text>,
        }}
      />
      <Drawer.Screen
        name="StatementImport"
        component={StatementImportScreen}
        options={{
          drawerLabel: 'Statement Import',
          drawerIcon: () => <Text style={{ fontSize: 20 }}>📄</Text>,
        }}
      />
      <Drawer.Screen
        name="NotificationCenter"
        component={NotificationCenterScreen}
        options={{
          drawerLabel: 'Notifications',
          drawerIcon: () => <Text style={{ fontSize: 20 }}>🔔</Text>,
        }}
      />
      <Drawer.Screen
        name="RecommendationsHistory"
        component={RecommendationsHistoryScreen}
        options={{
          drawerLabel: 'Recommendations',
          drawerIcon: () => <Text style={{ fontSize: 20 }}>💡</Text>,
        }}
      />
      <Drawer.Screen
        name="PremiumUpgrade"
        component={PremiumUpgradeScreen}
        options={{
          drawerLabel: 'Premium Upgrade',
          drawerIcon: () => <Text style={{ fontSize: 20 }}>⭐</Text>,
        }}
      />
      <Drawer.Screen
        name="Settings"
        component={SettingsScreen}
        options={{
          drawerLabel: 'Settings',
          drawerIcon: () => <Text style={{ fontSize: 20 }}>⚙️</Text>,
        }}
      />
      <Drawer.Screen
        name="Profile"
        component={ProfileScreen}
        options={{
          drawerLabel: 'Profile',
          drawerIcon: () => <Text style={{ fontSize: 20 }}>👤</Text>,
        }}
      />
      <Drawer.Screen
        name="AccountShares"
        component={AccountSharesScreen}
        options={{
          drawerLabel: 'Account Shares',
          drawerIcon: () => <Text style={{ fontSize: 20 }}>🤝</Text>,
        }}
      />
      <Drawer.Screen
        name="HelpSupport"
        component={HelpSupportScreen}
        options={{
          drawerLabel: 'Help & Support',
          drawerIcon: () => <Text style={{ fontSize: 20 }}>❓</Text>,
        }}
      />
    </Drawer.Navigator>
  );
};

const styles = StyleSheet.create({
  customTabBar: {
    flexDirection: 'row',
    backgroundColor: colors.background,
    borderTopColor: colors.border,
    borderTopWidth: 1,
    height: 80,
    paddingBottom: 10,
    paddingTop: 5,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  tabButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 5,
  },
  tabIconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabIcon: {
    fontSize: 24,
    marginBottom: 2,
  },
  tabLabel: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
  },
});

export default MainNavigator;