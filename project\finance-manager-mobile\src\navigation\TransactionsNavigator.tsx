import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import TransactionsListScreen from '../screens/transactions/TransactionsListScreen';
import TransactionDetailScreen from '../screens/transactions/TransactionDetailScreen';
import AddEditTransactionScreen from '../screens/transactions/AddEditTransactionScreen';
import TransactionCalendarScreen from '../screens/transactions/TransactionCalendarScreen';
import BulkOperationsScreen from '../screens/transactions/BulkOperationsScreen';

const Stack = createStackNavigator();

const TransactionsNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name="TransactionsList" component={TransactionsListScreen} />
      <Stack.Screen name="TransactionDetail" component={TransactionDetailScreen} />
      <Stack.Screen name="AddEditTransaction" component={AddEditTransactionScreen} />
      <Stack.Screen name="TransactionCalendar" component={TransactionCalendarScreen} />
      <Stack.Screen name="BulkOperations" component={BulkOperationsScreen} />
    </Stack.Navigator>
  );
};

export default TransactionsNavigator;