import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
} from 'react-native';
import { useAppDispatch } from '../../hooks/useAppDispatch';
import { useTypedSelector } from '../../hooks/useTypedSelector';
import { fetchDashboardInsights } from '../../store/slices/analyticsSlice';
import { fetchAccounts } from '../../store/slices/accountsSlice';
import { fetchTransactions } from '../../store/slices/transactionsSlice';
import { fetchGoals } from '../../store/slices/goalsSlice';
import { fetchRecommendations } from '../../store/slices/recommendationsSlice';
import { fetchUnreadNotifications } from '../../store/slices/notificationsSlice';
import { BalanceCard } from '../../components/common/BalanceCard';
import { TransactionItem } from '../../components/common/TransactionItem';
import { GoalCard } from '../../components/common/GoalCard';
import { RecommendationCard } from '../../components/common/RecommendationCard';
import { PieChart } from '../../components/charts/PieChart';
import { LineChart } from '../../components/charts/LineChart';
import { LoadingSpinner } from '../../components/common/LoadingSpinner';
import { colors, typography, spacing } from '../../constants/colors';
import { getDefaultCurrency } from '../../utils/currency';

interface DashboardScreenProps {
  navigation: any;
}

const DashboardScreen: React.FC<DashboardScreenProps> = ({ navigation }) => {
  const dispatch = useAppDispatch();
  const [refreshing, setRefreshing] = useState(false);

  const { user, isAuthenticated } = useTypedSelector((state) => state.auth);
  const { accounts } = useTypedSelector((state) => state.accounts);
  const { transactions } = useTypedSelector((state) => state.transactions);
  const { goals } = useTypedSelector((state) => state.goals);
  const { recommendations } = useTypedSelector((state) => state.recommendations);
  const { unreadCount } = useTypedSelector((state) => state.notifications);
  const { dashboardInsights, isLoading } = useTypedSelector((state) => state.analytics);

  useEffect(() => {
    // Only load dashboard data when user is authenticated
    if (isAuthenticated) {
      loadDashboardData();
    }
  }, [isAuthenticated]);

  const loadDashboardData = async () => {
    // Double-check authentication before making API calls
    if (!isAuthenticated) {
      console.log('🚫 Skipping dashboard data load - user not authenticated');
      return;
    }

    try {
      console.log('📊 Loading dashboard data for authenticated user');
      await Promise.all([
        dispatch(fetchDashboardInsights()),
        dispatch(fetchAccounts()),
        dispatch(fetchTransactions({ limit: 10 })),
        dispatch(fetchGoals()),
        dispatch(fetchRecommendations()),
        dispatch(fetchUnreadNotifications()),
      ]);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    }
  };

  const onRefresh = async () => {
    if (!isAuthenticated) {
      console.log('🚫 Skipping refresh - user not authenticated');
      return;
    }

    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const calculateTotalBalance = () => {
    return accounts.reduce((total, account) => total + account.balance, 0);
  };

  const getPrimaryCurrency = () => {
    // Use the currency from the first account, or default to USD
    if (accounts.length > 0 && accounts[0].currency) {
      return accounts[0].currency;
    }
    return getDefaultCurrency();
  };

  const getRecentTransactions = () => {
    return transactions.slice(0, 5);
  };

  const getActiveGoals = () => {
    return goals.filter(goal => goal.status === 'active').slice(0, 3);
  };

  const getTopRecommendations = () => {
    return recommendations.slice(0, 3);
  };

  const getCategoryBreakdownData = () => {
    if (!dashboardInsights?.top_categories || dashboardInsights.top_categories.length === 0) {
      return [];
    }
    
    return dashboardInsights.top_categories.map((category: any, index: number) => ({
      name: category.name,
      amount: category.amount,
      color: colors.categories[index % colors.categories.length],
    }));
  };

  const getSpendingTrendData = () => {
    // Always return a valid data structure for the chart
    if (!dashboardInsights?.spending_trend) {
      // Return default data with a single transparent dataset to prevent chart errors
      return {
        labels: ['No Data'],
        datasets: [{
          data: [0],
          color: (opacity = 1) => `rgba(0, 0, 0, 0)`, // Transparent
          strokeWidth: 0,
        }],
      };
    }
    
    // Mock data for demonstration - replace with actual trend data when available
    return {
      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
      datasets: [{
        data: [20000, 25000, 22000, 28000, 24000, 26000],
        color: (opacity = 1) => `rgba(46, 125, 87, ${opacity})`,
        strokeWidth: 2,
      }],
    };
  };

  const handleDismissRecommendation = (id: string) => {
    // Handle recommendation dismissal
    console.log('Dismiss recommendation:', id);
  };

  const handleActOnRecommendation = (id: string) => {
    // Handle recommendation action
    console.log('Act on recommendation:', id);
  };

  // Show loading spinner if not authenticated or if loading and no data
  if (!isAuthenticated || (isLoading && !dashboardInsights)) {
    return <LoadingSpinner />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.welcomeSection}>
            <Text style={styles.welcomeText}>
              Welcome back, {user?.first_name || 'User'}!
            </Text>
            <Text style={styles.dateText}>
              {new Date().toLocaleDateString('en-IN', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
              })}
            </Text>
          </View>
          <TouchableOpacity
            style={styles.notificationButton}
            onPress={() => navigation.navigate('NotificationCenter')}
          >
            <Text style={styles.notificationIcon}>🔔</Text>
            {unreadCount > 0 && (
              <View style={styles.notificationBadge}>
                <Text style={styles.notificationBadgeText}>
                  {unreadCount > 99 ? '99+' : unreadCount}
                </Text>
              </View>
            )}
          </TouchableOpacity>
        </View>

        {/* Total Balance */}
        <BalanceCard
          title="Total Balance"
          balance={calculateTotalBalance()}
          currency={getPrimaryCurrency()}
          subtitle={`Across ${accounts.length} accounts`}
          showTrend={true}
          trendValue={dashboardInsights?.spending_trend?.change_percentage}
          trendDirection={
            dashboardInsights?.spending_trend?.trend_direction === 'increasing' ? 'up' : 'down'
          }
        />

        {/* Quick Actions */}
        <View style={styles.quickActions}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate('Transactions', { screen: 'AddEditTransaction' })}
            >
              <Text style={styles.actionIcon}>💰</Text>
              <Text style={styles.actionText}>Add Transaction</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate('Goals', { screen: 'AddManualGoal' })}
            >
              <Text style={styles.actionIcon}>🎯</Text>
              <Text style={styles.actionText}>Create Goal</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate('Accounts', { screen: 'AddEditAccount' })}
            >
              <Text style={styles.actionIcon}>🏦</Text>
              <Text style={styles.actionText}>Add Account</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate('StatementImport')}
            >
              <Text style={styles.actionIcon}>📄</Text>
              <Text style={styles.actionText}>Import</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Spending Trend Chart - Only show if we have valid data */}
        {dashboardInsights?.spending_trend && (
          <LineChart
            data={getSpendingTrendData()}
            title="Spending Trend (Last 6 Months)"
            yAxisSuffix={getPrimaryCurrency()}
          />
        )}

        {/* Category Breakdown - Only show if we have data */}
        {getCategoryBreakdownData().length > 0 && (
          <PieChart
            data={getCategoryBreakdownData()}
            title="Category Breakdown (This Month)"
          />
        )}

        {/* Recent Transactions */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Recent Transactions</Text>
            <TouchableOpacity
              onPress={() => navigation.navigate('Transactions')}
            >
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>
          {getRecentTransactions().length > 0 ? (
            getRecentTransactions().map((transaction) => (
              <TransactionItem
                key={transaction.id}
                transaction={transaction}
                onPress={() =>
                  navigation.navigate('Transactions', {
                    screen: 'TransactionDetail',
                    params: { transactionId: transaction.id },
                  })
                }
              />
            ))
          ) : (
            <View style={styles.emptyState}>
              <Text style={styles.emptyIcon}>📋</Text>
              <Text style={styles.emptyTitle}>No Transactions Yet</Text>
              <Text style={styles.emptyMessage}>
                Add your first transaction to get started
              </Text>
            </View>
          )}
        </View>

        {/* Active Goals */}
        {getActiveGoals().length > 0 && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Active Goals</Text>
              <TouchableOpacity
                onPress={() => navigation.navigate('Goals')}
              >
                <Text style={styles.seeAllText}>See All</Text>
              </TouchableOpacity>
            </View>
            {getActiveGoals().map((goal) => (
              <GoalCard
                key={goal.id}
                goal={goal}
                compact={true}
                onPress={() =>
                  navigation.navigate('Goals', {
                    screen: 'GoalDetail',
                    params: { goalId: goal.id },
                  })
                }
              />
            ))}
          </View>
        )}

        {/* Recommendations */}
        {getTopRecommendations().length > 0 && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Recommendations</Text>
              <TouchableOpacity
                onPress={() => navigation.navigate('RecommendationsHistory')}
              >
                <Text style={styles.seeAllText}>See All</Text>
              </TouchableOpacity>
            </View>
            {getTopRecommendations().map((recommendation) => (
              <RecommendationCard
                key={recommendation.id}
                recommendation={recommendation}
                onDismiss={() => handleDismissRecommendation(recommendation.id)}
                onAct={() => handleActOnRecommendation(recommendation.id)}
              />
            ))}
          </View>
        )}

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.lg,
    paddingBottom: spacing.md,
  },
  welcomeSection: {
    flex: 1,
  },
  welcomeText: {
    ...typography.h2,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  dateText: {
    ...typography.caption,
    color: colors.textSecondary,
  },
  notificationButton: {
    position: 'relative',
    padding: spacing.sm,
  },
  notificationIcon: {
    fontSize: 24,
  },
  notificationBadge: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: colors.error,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  notificationBadgeText: {
    ...typography.small,
    color: colors.background,
    fontWeight: 'bold',
  },
  quickActions: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    ...typography.h3,
    color: colors.text,
    marginBottom: spacing.md,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flex: 1,
    backgroundColor: colors.card,
    borderRadius: 12,
    padding: spacing.md,
    alignItems: 'center',
    marginHorizontal: spacing.xs,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  actionIcon: {
    fontSize: 24,
    marginBottom: spacing.sm,
  },
  actionText: {
    ...typography.small,
    color: colors.text,
    textAlign: 'center',
    fontWeight: '600',
  },
  section: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.lg,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  seeAllText: {
    ...typography.caption,
    color: colors.primary,
    fontWeight: '600',
  },
  emptyState: {
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: spacing.xl,
    alignItems: 'center',
  },
  emptyIcon: {
    fontSize: 48,
    marginBottom: spacing.md,
  },
  emptyTitle: {
    ...typography.h3,
    color: colors.text,
    marginBottom: spacing.sm,
    textAlign: 'center',
  },
  emptyMessage: {
    ...typography.body,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
  },
  bottomSpacing: {
    height: spacing.xl,
  },
});

export default DashboardScreen;