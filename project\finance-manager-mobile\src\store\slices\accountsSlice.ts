import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { apiService } from '../../services/api';

interface AccountsState {
  accounts: any[];
  selectedAccount: any | null;
  balanceHistory: any[];
  isLoading: boolean;
  error: string | null;
}

const initialState: AccountsState = {
  accounts: [],
  selectedAccount: null,
  balanceHistory: [],
  isLoading: false,
  error: null,
};

export const fetchAccounts = createAsyncThunk(
  'accounts/fetchAccounts',
  async () => {
    const response = await apiService.getAccounts();
    return response;
  }
);

export const fetchAccount = createAsyncThunk(
  'accounts/fetchAccount',
  async (id: string) => {
    const response = await apiService.getAccount(id);
    return response;
  }
);

export const createAccount = createAsyncThunk(
  'accounts/createAccount',
  async (accountData: any) => {
    const response = await apiService.createAccount(accountData);
    return response;
  }
);

export const updateAccount = createAsyncThunk(
  'accounts/updateAccount',
  async ({ id, data }: { id: string; data: any }) => {
    const response = await apiService.updateAccount(id, data);
    return response;
  }
);

export const deleteAccount = createAsyncThunk(
  'accounts/deleteAccount',
  async (id: string) => {
    await apiService.deleteAccount(id);
    return id;
  }
);

export const fetchBalanceHistory = createAsyncThunk(
  'accounts/fetchBalanceHistory',
  async ({ id, days }: { id: string; days?: number }) => {
    const response = await apiService.getAccountBalanceHistory(id, days);
    return response;
  }
);

const accountsSlice = createSlice({
  name: 'accounts',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setSelectedAccount: (state, action) => {
      state.selectedAccount = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch accounts
      .addCase(fetchAccounts.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchAccounts.fulfilled, (state, action) => {
        state.isLoading = false;
        state.accounts = action.payload.accounts;
      })
      .addCase(fetchAccounts.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch accounts';
      })
      // Fetch single account
      .addCase(fetchAccount.fulfilled, (state, action) => {
        state.selectedAccount = action.payload.account;
      })
      // Create account
      .addCase(createAccount.fulfilled, (state, action) => {
        state.accounts.push(action.payload.account);
      })
      // Update account
      .addCase(updateAccount.fulfilled, (state, action) => {
        const index = state.accounts.findIndex(acc => acc.id === action.payload.account.id);
        if (index !== -1) {
          state.accounts[index] = action.payload.account;
        }
      })
      // Delete account
      .addCase(deleteAccount.fulfilled, (state, action) => {
        state.accounts = state.accounts.filter(acc => acc.id !== action.payload);
      })
      // Fetch balance history
      .addCase(fetchBalanceHistory.fulfilled, (state, action) => {
        state.balanceHistory = action.payload.history;
      });
  },
});

export const { clearError, setSelectedAccount } = accountsSlice.actions;
export default accountsSlice.reducer;