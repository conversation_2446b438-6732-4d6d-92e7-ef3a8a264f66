import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { apiService } from '../../services/api';

interface AnalyticsState {
  spendingTrends: any[];
  categoryBreakdown: any[];
  dashboardInsights: any | null;
  weeklyReport: any | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: AnalyticsState = {
  spendingTrends: [],
  categoryBreakdown: [],
  dashboardInsights: null,
  weeklyReport: null,
  isLoading: false,
  error: null,
};

export const fetchSpendingTrends = createAsyncThunk(
  'analytics/fetchSpendingTrends',
  async (months = 6) => {
    const response = await apiService.getSpendingTrends(months);
    return response;
  }
);

export const fetchCategoryBreakdown = createAsyncThunk(
  'analytics/fetchCategoryBreakdown',
  async ({ startDate, endDate }: { startDate: string; endDate: string }) => {
    const response = await apiService.getCategoryBreakdown(startDate, endDate);
    return response;
  }
);

export const fetchDashboardInsights = createAsyncThunk(
  'analytics/fetchDashboardInsights',
  async () => {
    const response = await apiService.getDashboardInsights();
    return response;
  }
);

export const fetchWeeklyReport = createAsyncThunk(
  'analytics/fetchWeeklyReport',
  async () => {
    const response = await apiService.getWeeklyReport();
    return response;
  }
);

const analyticsSlice = createSlice({
  name: 'analytics',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch spending trends
      .addCase(fetchSpendingTrends.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchSpendingTrends.fulfilled, (state, action) => {
        state.isLoading = false;
        state.spendingTrends = action.payload.trends;
      })
      .addCase(fetchSpendingTrends.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch spending trends';
      })
      // Fetch category breakdown
      .addCase(fetchCategoryBreakdown.fulfilled, (state, action) => {
        state.categoryBreakdown = action.payload.breakdown;
      })
      // Fetch dashboard insights
      .addCase(fetchDashboardInsights.fulfilled, (state, action) => {
        state.dashboardInsights = action.payload;
      })
      // Fetch weekly report
      .addCase(fetchWeeklyReport.fulfilled, (state, action) => {
        state.weeklyReport = action.payload;
      });
  },
});

export const { clearError } = analyticsSlice.actions;
export default analyticsSlice.reducer;