import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { apiService } from '../../services/api';

interface CategoriesState {
  categories: any[];
  hierarchy: any[];
  isLoading: boolean;
  error: string | null;
}

const initialState: CategoriesState = {
  categories: [],
  hierarchy: [],
  isLoading: false,
  error: null,
};

export const fetchCategories = createAsyncThunk(
  'categories/fetchCategories',
  async () => {
    const response = await apiService.getCategories();
    return response;
  }
);

export const fetchCategoryHierarchy = createAsyncThunk(
  'categories/fetchHierarchy',
  async () => {
    const response = await apiService.getCategoryHierarchy();
    return response;
  }
);

export const createCategory = createAsyncThunk(
  'categories/createCategory',
  async (categoryData: any) => {
    const response = await apiService.createCategory(categoryData);
    return response;
  }
);

const categoriesSlice = createSlice({
  name: 'categories',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch categories
      .addCase(fetchCategories.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchCategories.fulfilled, (state, action) => {
        state.isLoading = false;
        state.categories = action.payload.categories;
      })
      .addCase(fetchCategories.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch categories';
      })
      // Fetch hierarchy
      .addCase(fetchCategoryHierarchy.fulfilled, (state, action) => {
        state.hierarchy = action.payload.hierarchy;
      })
      // Create category
      .addCase(createCategory.fulfilled, (state, action) => {
        state.categories.push(action.payload.category);
      });
  },
});

export const { clearError } = categoriesSlice.actions;
export default categoriesSlice.reducer;