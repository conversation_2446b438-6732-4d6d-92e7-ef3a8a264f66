import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { apiService } from '../../services/api';

interface GoalsState {
  goals: any[];
  selectedGoal: any | null;
  aiSession: any | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: GoalsState = {
  goals: [],
  selectedGoal: null,
  aiSession: null,
  isLoading: false,
  error: null,
};

export const fetchGoals = createAsyncThunk(
  'goals/fetchGoals',
  async () => {
    const response = await apiService.getGoals();
    return response;
  }
);

export const fetchGoal = createAsyncThunk(
  'goals/fetchGoal',
  async (id: string) => {
    const response = await apiService.getGoal(id);
    return response;
  }
);

export const createGoal = createAsyncThunk(
  'goals/createGoal',
  async (goalData: any) => {
    const response = await apiService.createGoal(goalData);
    return response;
  }
);

export const contributeToGoal = createAsyncThunk(
  'goals/contributeToGoal',
  async ({ id, amount }: { id: string; amount: number }) => {
    const response = await apiService.contributeToGoal(id, amount);
    return response;
  }
);

export const startAIGoalSession = createAsyncThunk(
  'goals/startAISession',
  async () => {
    const response = await apiService.startAIGoalSession();
    return response;
  }
);

export const chatWithAI = createAsyncThunk(
  'goals/chatWithAI',
  async ({ sessionId, message }: { sessionId: string; message: string }) => {
    const response = await apiService.chatWithAI(sessionId, message);
    return response;
  }
);

export const finalizeAIGoal = createAsyncThunk(
  'goals/finalizeAIGoal',
  async (sessionId: string) => {
    const response = await apiService.finalizeAIGoal(sessionId);
    return response;
  }
);

const goalsSlice = createSlice({
  name: 'goals',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setSelectedGoal: (state, action) => {
      state.selectedGoal = action.payload;
    },
    clearAISession: (state) => {
      state.aiSession = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch goals
      .addCase(fetchGoals.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchGoals.fulfilled, (state, action) => {
        state.isLoading = false;
        state.goals = action.payload.goals;
      })
      .addCase(fetchGoals.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch goals';
      })
      // Fetch single goal
      .addCase(fetchGoal.fulfilled, (state, action) => {
        state.selectedGoal = action.payload.goal;
      })
      // Create goal
      .addCase(createGoal.fulfilled, (state, action) => {
        state.goals.push(action.payload.goal);
      })
      // Contribute to goal
      .addCase(contributeToGoal.fulfilled, (state, action) => {
        const index = state.goals.findIndex(g => g.id === action.payload.goal.id);
        if (index !== -1) {
          state.goals[index] = action.payload.goal;
        }
      })
      // AI Session
      .addCase(startAIGoalSession.fulfilled, (state, action) => {
        state.aiSession = action.payload;
      })
      .addCase(chatWithAI.fulfilled, (state, action) => {
        state.aiSession = action.payload;
      })
      .addCase(finalizeAIGoal.fulfilled, (state, action) => {
        state.goals.push(action.payload.goal);
        state.aiSession = null;
      });
  },
});

export const { clearError, setSelectedGoal, clearAISession } = goalsSlice.actions;
export default goalsSlice.reducer;