import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { apiService } from '../../services/api';

interface RecommendationsState {
  recommendations: any[];
  history: any[];
  isLoading: boolean;
  error: string | null;
}

const initialState: RecommendationsState = {
  recommendations: [],
  history: [],
  isLoading: false,
  error: null,
};

export const fetchRecommendations = createAsyncThunk(
  'recommendations/fetchRecommendations',
  async () => {
    const response = await apiService.getRecommendations();
    return response;
  }
);

export const dismissRecommendation = createAsyncThunk(
  'recommendations/dismissRecommendation',
  async (id: string) => {
    await apiService.dismissRecommendation(id);
    return id;
  }
);

export const actOnRecommendation = createAsyncThunk(
  'recommendations/actOnRecommendation',
  async (id: string) => {
    await apiService.actOnRecommendation(id);
    return id;
  }
);

const recommendationsSlice = createSlice({
  name: 'recommendations',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch recommendations
      .addCase(fetchRecommendations.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchRecommendations.fulfilled, (state, action) => {
        state.isLoading = false;
        state.recommendations = action.payload.recommendations;
      })
      .addCase(fetchRecommendations.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch recommendations';
      })
      // Dismiss recommendation
      .addCase(dismissRecommendation.fulfilled, (state, action) => {
        state.recommendations = state.recommendations.filter(r => r.id !== action.payload);
      })
      // Act on recommendation
      .addCase(actOnRecommendation.fulfilled, (state, action) => {
        const index = state.recommendations.findIndex(r => r.id === action.payload);
        if (index !== -1) {
          state.recommendations[index].status = 'acted';
        }
      });
  },
});

export const { clearError } = recommendationsSlice.actions;
export default recommendationsSlice.reducer;