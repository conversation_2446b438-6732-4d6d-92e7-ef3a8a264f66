import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { apiService } from '../../services/api';

interface TransactionsState {
  transactions: any[];
  calendarData: any;
  pagination: any;
  isLoading: boolean;
  error: string | null;
}

const initialState: TransactionsState = {
  transactions: [],
  calendarData: null,
  pagination: null,
  isLoading: false,
  error: null,
};

export const fetchTransactions = createAsyncThunk(
  'transactions/fetchTransactions',
  async ({ page = 1, limit = 20 }: { page?: number; limit?: number } = {}) => {
    const response = await apiService.getTransactions(page, limit);
    return response;
  }
);

export const fetchTransactionCalendar = createAsyncThunk(
  'transactions/fetchCalendar',
  async ({ year, month }: { year: number; month: number }) => {
    const response = await apiService.getTransactionCalendar(year, month);
    return response;
  }
);

export const createTransaction = createAsyncThunk(
  'transactions/createTransaction',
  async (transactionData: any) => {
    const response = await apiService.createTransaction(transactionData);
    return response;
  }
);

export const updateTransaction = createAsyncThunk(
  'transactions/updateTransaction',
  async ({ id, data }: { id: string; data: any }) => {
    const response = await apiService.updateTransaction(id, data);
    return response;
  }
);

export const deleteTransaction = createAsyncThunk(
  'transactions/deleteTransaction',
  async (id: string) => {
    await apiService.deleteTransaction(id);
    return id;
  }
);

const transactionsSlice = createSlice({
  name: 'transactions',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    addTransaction: (state, action) => {
      state.transactions.unshift(action.payload);
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch transactions
      .addCase(fetchTransactions.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchTransactions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.transactions = action.payload.transactions;
        state.pagination = action.payload.pagination;
      })
      .addCase(fetchTransactions.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch transactions';
      })
      // Fetch calendar
      .addCase(fetchTransactionCalendar.fulfilled, (state, action) => {
        state.calendarData = action.payload;
      })
      // Create transaction
      .addCase(createTransaction.fulfilled, (state, action) => {
        state.transactions.unshift(action.payload.transaction);
      })
      // Update transaction
      .addCase(updateTransaction.fulfilled, (state, action) => {
        const index = state.transactions.findIndex(t => t.id === action.payload.transaction.id);
        if (index !== -1) {
          state.transactions[index] = action.payload.transaction;
        }
      })
      // Delete transaction
      .addCase(deleteTransaction.fulfilled, (state, action) => {
        state.transactions = state.transactions.filter(t => t.id !== action.payload);
      });
  },
});

export const { clearError, addTransaction } = transactionsSlice.actions;
export default transactionsSlice.reducer;